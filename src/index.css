@import url("https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&display=swap");
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Bangladeshi SIM Offer Marketplace Design System */

@layer base {
  :root {
    /* New Impressive Design System Colors */
    --primary: #fe5933;
    --cta: #2c2c2c;
    --cta-gold: #fccc41;
    --radius: 0.625rem;
    --background: #f9f9f9;
    --foreground: oklch(0.145 0 0);

    /* Card colors */
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);

    /* Primary colors */
    --primary-foreground: oklch(0.985 0 0);

    /* Secondary colors */
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);

    /* Muted colors */
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);

    /* Accent colors */
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);

    /* Status colors */
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(1.0000 0 0);

    --success: 142 76% 36%;
    --warning: 43 96% 56%;
    --info: 217 91% 60%;

    /* Borders and inputs */
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);

    /* Chart colors */
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);

    /* Sidebar colors */
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);

    /* Custom marketplace colors */
    --operator-gp: 142 76% 36%;
    --operator-robi: 14 100% 57%;
    --operator-banglalink: 203 89% 53%;
    --operator-airtel: 0 84% 60%;
    --operator-skitto: 280 100% 70%;

    --text-light: 220 9% 83%;
    --text-muted: 220 9% 68%;
    --surface-elevated: 220 13% 20%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(158 64% 45%));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(220 13% 16%));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 76% 30%));

    /* Shadows */
    --shadow-card: 0 4px 12px -2px hsl(220 13% 8% / 0.4);
    --shadow-elevated: 0 8px 25px -4px hsl(220 13% 8% / 0.6);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);

    /* Animations */
    --transition-smooth: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: oklch(0.2101 0.0318 264.6645);

    --sidebar-primary: oklch(0.5960 0.1274 163.2254);

    --sidebar-primary-foreground: oklch(1.0000 0 0);

    --sidebar-accent: oklch(0.5960 0.1274 163.2254);

    --sidebar-accent-foreground: oklch(1.0000 0 0);

    --sidebar-border: oklch(0.9276 0.0058 264.5313);

    --sidebar-ring: oklch(0.5960 0.1274 163.2254);

    --chart-1: oklch(0.6959 0.1491 162.4796);

    --chart-2: oklch(0.6231 0.1880 259.8145);

    --chart-3: oklch(0.6368 0.2078 25.3313);

    --chart-4: oklch(0.7049 0.1867 47.6044);

    --chart-5: oklch(0.6056 0.2189 292.7172);

    --sidebar: oklch(0.9846 0.0017 247.8389);

    --font-sans: var(--font-geist-sans);
    --font-serif: Lora;
    --font-mono: var(--font-geist-mono);
    --font-bricolage: "Bricolage Grotesque", sans-serif;

    --shadow-color: #000000;

    --shadow-opacity: 0.05;

    --shadow-blur: 4px;

    --shadow-spread: 0px;

    --shadow-offset-x: 0px;

    --shadow-offset-y: 2px;

    --letter-spacing: 0rem;

    --spacing: 0.25rem;

    --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.03);

    --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.03);

    --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);

    --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);

    --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 2px 4px -1px hsl(0 0% 0% / 0.05);

    --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 4px 6px -1px hsl(0 0% 0% / 0.05);

    --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 8px 10px -1px hsl(0 0% 0% / 0.05);

    --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.13);

    --tracking-normal: 0rem;
  }

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-cta: var(--cta);
  --color-cta-gold: var(--cta-gold);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-bricolage: "Bricolage Grotesque", sans-serif;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

  .theme {
    --font-sans: Geist;
    --font-mono: Geist Mono;
    --font-serif: Lora;
    --radius: 0.5rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  .dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
    --font-sans: Geist;
    --font-serif: Lora;
    --font-mono: Geist Mono;
    --shadow-color: #000000;
    --shadow-opacity: 0.1;
    --shadow-blur: 4px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0rem;
    --spacing: 0.25rem;
    --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  }
  body {
    letter-spacing: var(--tracking-normal);
    font-family: var(--font-bricolage);
  }

  main {
    @apply mx-auto px-14 flex flex-col gap-8 bg-background h-full max-w-[1400px] pt-10 max-sm:px-2 mb-5;
  }

  h1 {
    @apply text-3xl font-bold;
  }
}

@layer components {
  /* Custom Marketplace Components */
  .offer-card {
    @apply bg-card border border-border rounded-2xl p-4 shadow-md transition-all duration-200 hover:shadow-lg hover:scale-[1.03] hover:border-primary/40;
    /* Mobile-first, expressive elevation, soft corners, smooth scale */
  }

  /* MCP Responsive Guidance: 
     For responsiveness, use shadcn/ui components (Card, Button, Tabs, etc.) in your React code and apply Tailwind responsive classes (sm:, md:, lg:) directly in JSX. 
     Avoid duplicating responsive logic in global CSS. 
     Example:
     <Card className="w-full sm:max-w-md md:max-w-lg p-4 sm:p-6 md:p-8" />
     <Button className="w-full sm:w-auto h-10 sm:h-11 md:h-12" />
     <Tabs className="flex-col md:flex-row" />
  */

  .price-original {
    @apply text-muted-foreground text-base line-through;
    /* Slightly larger for mobile clarity */
  }

  .price-current {
    @apply text-primary text-xl font-bold;
    /* Bolder, larger for mobile */
  }

  .savings-badge {
    @apply bg-success/20 text-success px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
    /* Rounded, expressive, mobile-first */
  }

  .region-badge {
    @apply bg-warning/20 text-warning px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
  }

  .combo-badge {
    @apply bg-success/20 text-success px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
  }

  .shimmer {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted rounded-xl;
  }

  .mobile-container {
    @apply w-full max-w-full mx-auto bg-background min-h-screen px-2 sm:px-0 flex flex-col gap-4;
    /* Mobile-first, vertical spacing, full width */
  }

  .stats-text {
    @apply text-muted-foreground text-base;
  }

  .balance-display {
    @apply text-primary font-bold text-base flex items-center gap-2;
  }

  /* Typography System */
  .heading-xl { @apply text-2xl font-bold leading-tight; }
  .heading-lg { @apply text-xl sm:text-2xl font-bold leading-tight tracking-tight; }
  .heading-md { @apply text-lg sm:text-xl font-semibold leading-tight tracking-tight; }
  .heading-sm { @apply text-base sm:text-lg font-semibold leading-tight tracking-tight; }

  .body-lg { @apply text-base leading-relaxed; }
  .body-md { @apply text-sm sm:text-base leading-relaxed tracking-normal; }
  .body-sm { @apply text-xs sm:text-sm leading-relaxed tracking-normal; }

  .text-muted { @apply text-muted-foreground; }
  .text-light { @apply text-[hsl(var(--text-light))]; }

  /* Admin Panel Styles */
  .admin-card {
    @apply bg-white border border-gray-200 rounded-lg p-6 shadow-sm;
  }

  .upload-zone {
    @apply border-2 border-dashed border-gray-300 rounded-lg p-8
           text-center hover:border-blue-400 transition-colors
           bg-gray-50 hover:bg-gray-100;
  }

  /* Scrollbar utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* New Impressive Design System Components */
  .home-section {
    @apply flex gap-4 justify-between items-start w-full max-lg:flex-col-reverse max-lg:items-center;
  }

  .companions-grid {
    @apply flex flex-wrap gap-4 w-full max-md:justify-center justify-between;
  }

  .companion-card {
    @apply flex flex-col rounded-4xl border border-black px-4 py-4 gap-5 w-full min-lg:max-w-[410px] justify-between;
  }

  .subject-badge {
    @apply bg-black text-white rounded-4xl text-sm px-2 py-1 capitalize;
  }

  .companion-bookmark {
    @apply px-2 bg-black rounded-4xl flex items-center h-full aspect-square cursor-pointer;
  }

  .input {
    @apply !border-black !bg-white focus-visible:!ring-0 focus-visible:!border-black !w-full;
  }

  .rounded-border {
    @apply rounded-4xl border border-black;
  }

  .cta-section {
    @apply bg-cta text-white rounded-4xl px-7 py-10 flex flex-col items-center text-center gap-5 w-1/3 max-lg:w-1/2 max-md:w-full;
  }

  .cta-badge {
    @apply bg-cta-gold rounded-4xl px-3 py-1.5 text-black;
  }

  .btn-primary {
    @apply bg-primary text-white rounded-xl cursor-pointer px-4 py-2 flex items-center gap-2;
  }

  .navbar {
    @apply flex items-center justify-between mx-auto w-full px-14 py-4 bg-white max-sm:px-4;
  }

  .btn-signin {
    @apply border border-black rounded-4xl px-4 py-2.5 text-sm font-semibold flex items-center gap-2 cursor-pointer;
  }

  .companion-list {
    @apply rounded-4xl border border-black px-7 pt-7 pb-10 max-lg:w-full bg-white;
  }

  .companion-limit {
    @apply items-center justify-center flex flex-col gap-4 w-full min-2xl:w-1/2 pt-20 text-center;
  }

  .companion-section {
    @apply border-2 border-orange-500 w-2/3 max-sm:w-full flex flex-col gap-4 justify-center items-center rounded-lg;
  }

  .companion-avatar {
    @apply size-[300px] flex items-center justify-center rounded-lg max-sm:size-[100px] mt-4;
  }

  .companion-lottie {
    @apply size-[300px] max-sm:size-[100px];
  }

  .user-section {
    @apply flex flex-col gap-4 w-1/3 max-sm:w-full max-sm:flex-row;
  }

  .user-avatar {
    @apply border-2 border-black flex flex-col gap-4 items-center rounded-lg py-8 max-sm:hidden;
  }

  .btn-mic {
    @apply border-2 border-black rounded-lg flex flex-col gap-2 items-center py-8 max-sm:py-2 cursor-pointer w-full;
  }

  .transcript {
    @apply relative flex flex-col gap-4 w-full items-center pt-10 flex-grow overflow-hidden;
  }

  .transcript-message {
    @apply overflow-y-auto w-full flex flex-col gap-4 max-sm:gap-2 pr-2 h-full text-2xl;
  }

  .transcript-fade {
    @apply pointer-events-none absolute bottom-0 left-0 right-0 h-40 max-sm:h-20 bg-gradient-to-t from-background via-background/90 to-transparent z-10;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}